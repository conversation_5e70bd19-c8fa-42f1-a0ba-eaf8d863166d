import { Stack, Loader, Center } from '@mantine/core';
import { Suspense, lazy } from 'react';

const AnalysisSummary = lazy(() => import('./AnalysisSummary'));
const SuggestedActions = lazy(() => import('./SuggestedActions'));

const LoadingFallback = () => (
  <Center p="md">
    <Loader size="sm" />
  </Center>
);
const IncidentAIAssistant = () => {
  return (
    <Stack gap="lg">
      <Suspense fallback={<LoadingFallback />}>
        <AnalysisSummary />
      </Suspense>
      <Suspense fallback={<LoadingFallback />}>
        <SuggestedActions />
      </Suspense>
    </Stack>
  );
};

export default IncidentAIAssistant;
