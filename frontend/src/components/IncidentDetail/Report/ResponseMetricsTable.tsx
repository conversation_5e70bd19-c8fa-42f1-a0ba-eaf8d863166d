import { Badge, Card, Table, Title } from '@mantine/core';
import type { IncidentMetric } from '../../../api/incidentApi';

interface ResponseMetricsTableProps {
  metric?: IncidentMetric;
}

const ResponseMetricsTable = ({ metric }: ResponseMetricsTableProps) => {
  // Utility function to convert duration string (HH:MM:SS) to minutes
  const convertDurationToMinutes = (
    duration: string | null | undefined,
  ): number | null => {
    if (!duration) return null;

    const parts = duration.split(':');
    if (parts.length !== 3) return null;

    const hours = parseInt(parts[0]) || 0;
    const minutes = parseInt(parts[1]) || 0;
    const seconds = parseInt(parts[2]) || 0;

    return hours * 60 + minutes + Math.round(seconds / 60);
  };

  const getStatusBadge = (actual: number | null, target: number) => {
    if (actual === null) {
      return (
        <Badge size="sm" color="gray" variant="light">
          N/A
        </Badge>
      );
    }

    const isWithinTarget = actual <= target;
    return (
      <Badge size="sm" color={isWithinTarget ? 'green' : 'red'} variant="light">
        {isWithinTarget ? 'within' : 'exceeded'}
      </Badge>
    );
  };

  const formatDuration = (minutes: number | null): string => {
    return minutes === null ? 'N/A' : `${minutes}m`;
  };

  // Define metric rows with targets
  const metricRows = [
    {
      phase: 'Reporting',
      actual: convertDurationToMinutes(metric?.time_to_report),
      target: 3,
    },
    {
      phase: 'Acknowledgment',
      actual: convertDurationToMinutes(metric?.time_to_acknowledge),
      target: 2,
    },
    {
      phase: 'Resolution',
      actual: convertDurationToMinutes(metric?.time_to_resolve),
      target: 15,
    },
    {
      phase: 'Closure',
      actual: convertDurationToMinutes(metric?.time_to_closure),
      target: 60,
    },
  ];

  return (
    <Card padding="lg" withBorder>
      <Title order={3} mb="md">
        Incident Response Time Metrics
      </Title>
      <Table>
        <Table.Thead>
          <Table.Tr>
            <Table.Th>Phase</Table.Th>
            <Table.Th>Actual</Table.Th>
            <Table.Th>Target</Table.Th>
            <Table.Th>Status</Table.Th>
          </Table.Tr>
        </Table.Thead>
        <Table.Tbody>
          {metricRows.map(row => (
            <Table.Tr key={row.phase}>
              <Table.Td>{row.phase}</Table.Td>
              <Table.Td>{formatDuration(row.actual)}</Table.Td>
              <Table.Td>{row.target}m</Table.Td>
              <Table.Td>{getStatusBadge(row.actual, row.target)}</Table.Td>
            </Table.Tr>
          ))}
        </Table.Tbody>
      </Table>
    </Card>
  );
};

export default ResponseMetricsTable;
